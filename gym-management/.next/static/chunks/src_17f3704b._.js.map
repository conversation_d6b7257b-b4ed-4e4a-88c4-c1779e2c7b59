{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/republic/gym-management/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n\n// For server-side operations\nexport const supabaseAdmin = createClient<Database>(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n)\n"], "names": [], "mappings": ";;;;AAGoB;AAHpB;;AAGA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAGrD,MAAM,gBAAgB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EACtC,aACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/republic/gym-management/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport { Database } from '@/lib/database.types'\n\ntype Profile = Database['public']['Tables']['profiles']['Row']\n\ninterface AuthContextType {\n  user: User | null\n  profile: Profile | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<Profile>) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<Profile | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user ?? null)\n      if (session?.user) {\n        fetchProfile(session.user.id)\n      } else {\n        setLoading(false)\n      }\n    })\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setUser(session?.user ?? null)\n      if (session?.user) {\n        await fetchProfile(session.user.id)\n      } else {\n        setProfile(null)\n        setLoading(false)\n      }\n    })\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching profile:', error)\n      } else {\n        setProfile(data)\n      }\n    } catch (error) {\n      console.error('Error fetching profile:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return { error: 'No user logged in' }\n\n    const { error } = await supabase\n      .from('profiles')\n      .update(updates)\n      .eq('id', user.id)\n\n    if (!error) {\n      setProfile(prev => prev ? { ...prev, ...updates } : null)\n    }\n\n    return { error }\n  }\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signIn,\n    signOut,\n    updateProfile,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;0CAAC;wBAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;wBAC5C;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,aAAa,QAAQ,IAAI,CAAC,EAAE;oBAC9B,OAAO;wBACL,WAAW;oBACb;gBACF;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,OAAO,OAAO;wBACxC;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;oBACpC,OAAO;wBACL,WAAW;wBACX,WAAW;oBACb;gBACF;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;QAAoB;QAE/C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,CAAC,OAAO;YACV,WAAW,CAAA,OAAQ,OAAO;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC,IAAI;QACtD;QAEA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GAzFgB;KAAA;AA2FT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}