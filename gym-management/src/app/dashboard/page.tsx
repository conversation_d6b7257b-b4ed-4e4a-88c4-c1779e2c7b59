'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { formatCurrency } from '@/lib/utils'
import {
  UsersIcon,
  CreditCardIcon,
  BanknotesIcon,
  CalendarDaysIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline'

interface DashboardStats {
  totalMembers: number
  activeMembers: number
  totalRevenue: number
  monthlyRevenue: number
  pendingPayments: number
  expiringMemberships: number
  todayAttendance: number
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    activeMembers: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    pendingPayments: 0,
    expiringMemberships: 0,
    todayAttendance: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      // Fetch total and active members
      const { data: members } = await supabase
        .from('members')
        .select('status')
      
      const totalMembers = members?.length || 0
      const activeMembers = members?.filter(m => m.status === 'active').length || 0

      // Fetch revenue data
      const { data: payments } = await supabase
        .from('payments')
        .select('amount, payment_date, status')
        .eq('status', 'paid')

      const totalRevenue = payments?.reduce((sum, p) => sum + p.amount, 0) || 0
      
      const currentMonth = new Date().toISOString().slice(0, 7)
      const monthlyRevenue = payments?.filter(p => 
        p.payment_date?.startsWith(currentMonth)
      ).reduce((sum, p) => sum + p.amount, 0) || 0

      // Fetch pending payments
      const { data: pendingPaymentsData } = await supabase
        .from('payments')
        .select('id')
        .in('status', ['pending', 'overdue'])
      
      const pendingPayments = pendingPaymentsData?.length || 0

      // Fetch expiring memberships (next 7 days)
      const nextWeek = new Date()
      nextWeek.setDate(nextWeek.getDate() + 7)
      
      const { data: expiringMembershipsData } = await supabase
        .from('memberships')
        .select('id')
        .eq('status', 'active')
        .lte('end_date', nextWeek.toISOString().split('T')[0])

      const expiringMemberships = expiringMembershipsData?.length || 0

      // Fetch today's attendance
      const today = new Date().toISOString().split('T')[0]
      const { data: attendanceData } = await supabase
        .from('attendance')
        .select('id')
        .eq('date', today)

      const todayAttendance = attendanceData?.length || 0

      setStats({
        totalMembers,
        activeMembers,
        totalRevenue,
        monthlyRevenue,
        pendingPayments,
        expiringMemberships,
        todayAttendance,
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      name: 'Total Members',
      value: stats.totalMembers,
      icon: UsersIcon,
      color: 'bg-blue-500',
      textColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      name: 'Active Members',
      value: stats.activeMembers,
      icon: UsersIcon,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      name: 'Monthly Revenue',
      value: formatCurrency(stats.monthlyRevenue),
      icon: BanknotesIcon,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      name: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue),
      icon: TrendingUpIcon,
      color: 'bg-purple-500',
      textColor: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      name: 'Pending Payments',
      value: stats.pendingPayments,
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
      textColor: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      name: 'Expiring Soon',
      value: stats.expiringMemberships,
      icon: CalendarDaysIcon,
      color: 'bg-orange-500',
      textColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      name: "Today's Attendance",
      value: stats.todayAttendance,
      icon: CalendarDaysIcon,
      color: 'bg-indigo-500',
      textColor: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to your gym management dashboard</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => (
          <div
            key={stat.name}
            className="relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:px-6 sm:py-6"
          >
            <dt>
              <div className={`absolute rounded-md p-3 ${stat.color}`}>
                <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              <p className="ml-16 truncate text-sm font-medium text-gray-500">
                {stat.name}
              </p>
            </dt>
            <dd className="ml-16 flex items-baseline">
              <p className={`text-2xl font-semibold ${stat.textColor}`}>
                {stat.value}
              </p>
            </dd>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Activity */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Quick Actions
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <button className="bg-indigo-50 hover:bg-indigo-100 text-indigo-700 font-medium py-3 px-4 rounded-lg transition-colors">
                Add New Member
              </button>
              <button className="bg-green-50 hover:bg-green-100 text-green-700 font-medium py-3 px-4 rounded-lg transition-colors">
                Record Payment
              </button>
              <button className="bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium py-3 px-4 rounded-lg transition-colors">
                Check-in Member
              </button>
              <button className="bg-purple-50 hover:bg-purple-100 text-purple-700 font-medium py-3 px-4 rounded-lg transition-colors">
                Generate Report
              </button>
            </div>
          </div>
        </div>

        {/* Alerts */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Alerts & Notifications
            </h3>
            <div className="space-y-3">
              {stats.pendingPayments > 0 && (
                <div className="flex items-center p-3 bg-red-50 rounded-lg">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-3" />
                  <span className="text-sm text-red-700">
                    {stats.pendingPayments} pending payments require attention
                  </span>
                </div>
              )}
              {stats.expiringMemberships > 0 && (
                <div className="flex items-center p-3 bg-orange-50 rounded-lg">
                  <CalendarDaysIcon className="h-5 w-5 text-orange-400 mr-3" />
                  <span className="text-sm text-orange-700">
                    {stats.expiringMemberships} memberships expiring soon
                  </span>
                </div>
              )}
              {stats.pendingPayments === 0 && stats.expiringMemberships === 0 && (
                <div className="flex items-center p-3 bg-green-50 rounded-lg">
                  <TrendingUpIcon className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-sm text-green-700">
                    All systems running smoothly!
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
