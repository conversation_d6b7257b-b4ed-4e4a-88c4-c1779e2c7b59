import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, addMonths, differenceInDays, isAfter, isBefore } from "date-fns"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number, currency: string = 'INR'): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

export function formatDate(date: string | Date, formatStr: string = 'dd/MM/yyyy'): string {
  return format(new Date(date), formatStr)
}

export function calculateMembershipEndDate(startDate: string, durationMonths: number): string {
  return format(addMonths(new Date(startDate), durationMonths), 'yyyy-MM-dd')
}

export function getMembershipStatus(endDate: string): 'active' | 'expired' | 'expiring_soon' {
  const today = new Date()
  const end = new Date(endDate)
  const daysUntilExpiry = differenceInDays(end, today)
  
  if (isAfter(today, end)) {
    return 'expired'
  } else if (daysUntilExpiry <= 7) {
    return 'expiring_soon'
  } else {
    return 'active'
  }
}

export function generateReceiptNumber(): string {
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `RCP-${timestamp.slice(-6)}-${random}`
}

export function calculateDiscount(
  amount: number,
  discountType: 'percentage' | 'fixed',
  discountValue: number,
  maxDiscount?: number
): number {
  let discount = 0
  
  if (discountType === 'percentage') {
    discount = (amount * discountValue) / 100
    if (maxDiscount && discount > maxDiscount) {
      discount = maxDiscount
    }
  } else {
    discount = discountValue
  }
  
  return Math.min(discount, amount)
}

export function getPaymentStatusColor(status: string): string {
  switch (status) {
    case 'paid':
      return 'text-green-600 bg-green-100'
    case 'pending':
      return 'text-yellow-600 bg-yellow-100'
    case 'overdue':
      return 'text-red-600 bg-red-100'
    case 'partial':
      return 'text-blue-600 bg-blue-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

export function getMembershipStatusColor(status: string): string {
  switch (status) {
    case 'active':
      return 'text-green-600 bg-green-100'
    case 'expired':
      return 'text-red-600 bg-red-100'
    case 'expiring_soon':
      return 'text-orange-600 bg-orange-100'
    case 'pending':
      return 'text-yellow-600 bg-yellow-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

export function generateMembershipCard(member: any, membership: any): string {
  // This would generate a membership card URL or data
  // For now, return a placeholder
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="400" height="250" xmlns="http://www.w3.org/2000/svg">
      <rect width="400" height="250" fill="#1f2937" rx="10"/>
      <text x="20" y="40" fill="white" font-size="18" font-weight="bold">Gym Membership Card</text>
      <text x="20" y="80" fill="white" font-size="14">Name: ${member.name}</text>
      <text x="20" y="110" fill="white" font-size="14">Member ID: ${member.id.slice(0, 8)}</text>
      <text x="20" y="140" fill="white" font-size="14">Plan: ${membership.plan_name}</text>
      <text x="20" y="170" fill="white" font-size="14">Valid Until: ${formatDate(membership.end_date)}</text>
      <text x="20" y="220" fill="#9ca3af" font-size="12">Present this card for gym access</text>
    </svg>
  `)}`
}

export function exportToCSV(data: any[], filename: string): void {
  if (!data.length) return
  
  const headers = Object.keys(data[0])
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header]
        return typeof value === 'string' && value.includes(',') 
          ? `"${value}"` 
          : value
      }).join(',')
    )
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${filename}.csv`
  link.click()
  window.URL.revokeObjectURL(url)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}
