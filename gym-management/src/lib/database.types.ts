export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          role: 'admin' | 'receptionist' | 'trainer'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          role?: 'admin' | 'receptionist' | 'trainer'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          role?: 'admin' | 'receptionist' | 'trainer'
          created_at?: string
          updated_at?: string
        }
      }
      members: {
        Row: {
          id: string
          name: string
          email: string | null
          phone: string
          age: number | null
          gender: 'male' | 'female' | 'other'
          address: string | null
          emergency_contact: string | null
          photo_url: string | null
          id_proof_url: string | null
          status: 'active' | 'inactive' | 'suspended'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          phone: string
          age?: number | null
          gender: 'male' | 'female' | 'other'
          address?: string | null
          emergency_contact?: string | null
          photo_url?: string | null
          id_proof_url?: string | null
          status?: 'active' | 'inactive' | 'suspended'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          phone?: string
          age?: number | null
          gender?: 'male' | 'female' | 'other'
          address?: string | null
          emergency_contact?: string | null
          photo_url?: string | null
          id_proof_url?: string | null
          status?: 'active' | 'inactive' | 'suspended'
          created_at?: string
          updated_at?: string
        }
      }
      membership_plans: {
        Row: {
          id: string
          name: string
          description: string | null
          duration_months: number
          price: number
          features: string[]
          category: 'basic' | 'premium' | 'family' | 'student'
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          duration_months: number
          price: number
          features?: string[]
          category: 'basic' | 'premium' | 'family' | 'student'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          duration_months?: number
          price?: number
          features?: string[]
          category?: 'basic' | 'premium' | 'family' | 'student'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      memberships: {
        Row: {
          id: string
          member_id: string
          plan_id: string
          start_date: string
          end_date: string
          renewal_date: string | null
          status: 'active' | 'expired' | 'pending'
          custom_price: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          member_id: string
          plan_id: string
          start_date: string
          end_date: string
          renewal_date?: string | null
          status?: 'active' | 'expired' | 'pending'
          custom_price?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          member_id?: string
          plan_id?: string
          start_date?: string
          end_date?: string
          renewal_date?: string | null
          status?: 'active' | 'expired' | 'pending'
          custom_price?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          member_id: string
          membership_id: string | null
          amount: number
          payment_method: 'cash' | 'card' | 'upi' | 'bank_transfer'
          status: 'paid' | 'pending' | 'overdue' | 'partial'
          payment_date: string | null
          due_date: string
          receipt_number: string
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          member_id: string
          membership_id?: string | null
          amount: number
          payment_method: 'cash' | 'card' | 'upi' | 'bank_transfer'
          status?: 'paid' | 'pending' | 'overdue' | 'partial'
          payment_date?: string | null
          due_date: string
          receipt_number: string
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          member_id?: string
          membership_id?: string | null
          amount?: number
          payment_method?: 'cash' | 'card' | 'upi' | 'bank_transfer'
          status?: 'paid' | 'pending' | 'overdue' | 'partial'
          payment_date?: string | null
          due_date?: string
          receipt_number?: string
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      offers: {
        Row: {
          id: string
          code: string
          name: string
          description: string | null
          discount_type: 'percentage' | 'fixed'
          discount_value: number
          min_amount: number | null
          max_discount: number | null
          start_date: string
          end_date: string
          usage_limit: number | null
          used_count: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          code: string
          name: string
          description?: string | null
          discount_type: 'percentage' | 'fixed'
          discount_value: number
          min_amount?: number | null
          max_discount?: number | null
          start_date: string
          end_date: string
          usage_limit?: number | null
          used_count?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          code?: string
          name?: string
          description?: string | null
          discount_type?: 'percentage' | 'fixed'
          discount_value?: number
          min_amount?: number | null
          max_discount?: number | null
          start_date?: string
          end_date?: string
          usage_limit?: number | null
          used_count?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      attendance: {
        Row: {
          id: string
          member_id: string
          check_in_time: string
          check_out_time: string | null
          date: string
          created_at: string
        }
        Insert: {
          id?: string
          member_id: string
          check_in_time: string
          check_out_time?: string | null
          date: string
          created_at?: string
        }
        Update: {
          id?: string
          member_id?: string
          check_in_time?: string
          check_out_time?: string | null
          date?: string
          created_at?: string
        }
      }
      gym_settings: {
        Row: {
          id: string
          gym_name: string
          logo_url: string | null
          address: string | null
          phone: string | null
          email: string | null
          working_hours: any
          tax_rate: number
          currency: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          gym_name: string
          logo_url?: string | null
          address?: string | null
          phone?: string | null
          email?: string | null
          working_hours?: any
          tax_rate?: number
          currency?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          gym_name?: string
          logo_url?: string | null
          address?: string | null
          phone?: string | null
          email?: string | null
          working_hours?: any
          tax_rate?: number
          currency?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
