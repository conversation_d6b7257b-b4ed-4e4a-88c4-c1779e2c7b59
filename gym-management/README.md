# 🏋️ Gym Management System

A comprehensive gym management solution built with Next.js, TypeScript, Tailwind CSS, and Supabase. This system provides complete member management, payment tracking, attendance monitoring, and business analytics for fitness centers.

## ✨ Features

### 🔐 Admin Panel Features
- **Role-based Authentication** (Admin, Receptionist, Trainer)
- **Secure Dashboard** with real-time analytics
- **Multi-user Support** with different permission levels

### 🧑‍💼 Member Management
- ✅ Add, update, or delete member profiles
- ✅ View member list with filters (active, expired, pending)
- ✅ Complete member details: Name, contact, age, gender, address, email, emergency contact
- ✅ Upload member photo and ID proof
- ✅ Member status tracking (active, inactive, suspended)

### 💳 Membership & Fee Tracking
- ✅ Assign and manage membership plans
- ✅ Record start date, end date, and renewal date
- ✅ Payment status tracking (Paid, Pending, Overdue)
- ✅ View fee history per member
- ✅ Set custom or promotional fees
- ✅ Add partial payments or installments
- ✅ Auto-generate receipts and invoices

### 🎟️ Offers & Discounts
- ✅ Add discount codes / seasonal offers
- ✅ Apply offers during membership creation
- ✅ Track which member availed which offer
- ✅ Set limited-time offers with expiry
- ✅ Define % or flat-rate discounts

### 🗓️ Attendance & Check-ins
- ✅ Daily check-in logs
- ✅ Member visit history and stats
- ✅ Attendance tracking and analytics

### 📦 Membership Plan Features
- ✅ Create custom plans (Monthly, Quarterly, Yearly, etc.)
- ✅ Assign features per plan (e.g., gym access, yoga, swimming)
- ✅ Price, duration, and validity configuration
- ✅ Auto alerts for plan expiry and renewal
- ✅ Multiple plan categories (Basic, Premium, Family, etc.)

### 📈 Reports & Analytics
- ✅ Income reports (daily, weekly, monthly)
- ✅ Active vs inactive members
- ✅ Membership growth trends
- ✅ Fees collected per period
- ✅ Offer usage and effectiveness
- ✅ Outstanding payments

### 🔔 Notifications & Alerts
- ✅ Dashboard alerts for expiring memberships
- ✅ New registrations tracking
- ✅ Pending dues monitoring
- ✅ System health notifications

### 🧾 Invoice & Receipt Generation
- ✅ Auto-generate PDF receipts
- ✅ Unique invoice number for tracking
- ✅ Tax calculations and GST support
- ✅ Professional invoice templates

### 📋 Admin Dashboard
- ✅ Graphical summary: Total members, income, dues, new joins
- ✅ Quick filters for expiring memberships, defaulters, etc.
- ✅ Search and sort by name, phone, membership plan
- ✅ Real-time statistics and KPIs

### 🛠️ Settings & Customization
- ✅ Add gym logo, brand name, address
- ✅ Set working hours, holiday calendar
- ✅ Define default tax & discount settings
- ✅ Staff role creation (admin, receptionist, trainer)

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd gym-management
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up Supabase**
   - Follow the detailed guide in `scripts/setup-supabase.md`
   - Create a Supabase project
   - Run the database schema
   - Configure environment variables

4. **Environment Setup**
```bash
cp .env.local.example .env.local
# Edit .env.local with your Supabase credentials
```

5. **Run the development server**
```bash
npm run dev
```

6. **Open your browser**
   - Navigate to [http://localhost:3000](http://localhost:3000)
   - Use demo credentials to login

## 🔑 Demo Credentials

- **Admin**: <EMAIL> / admin123
- **Receptionist**: <EMAIL> / reception123
- **Trainer**: <EMAIL> / trainer123

## 🏗️ Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Forms**: React Hook Form, Zod validation
- **Charts**: Recharts
- **PDF Generation**: jsPDF, html2canvas
- **Icons**: Heroicons, Lucide React
- **Notifications**: React Hot Toast

## 📁 Project Structure

```
gym-management/
├── src/
│   ├── app/                 # Next.js app directory
│   │   ├── dashboard/       # Dashboard pages
│   │   │   ├── members/     # Member management
│   │   │   ├── plans/       # Membership plans
│   │   │   ├── payments/    # Payment tracking
│   │   │   ├── attendance/  # Attendance logs
│   │   │   ├── offers/      # Offers & discounts
│   │   │   ├── reports/     # Analytics & reports
│   │   │   └── settings/    # System settings
│   │   ├── login/          # Authentication
│   │   └── globals.css     # Global styles
│   ├── components/         # Reusable components
│   │   ├── layout/        # Layout components
│   │   ├── ui/            # UI components
│   │   └── forms/         # Form components
│   ├── contexts/          # React contexts
│   └── lib/               # Utilities and configurations
├── supabase/             # Database schema
└── scripts/              # Setup scripts
```

## 🚀 Quick Start

1. **Clone and Install**
```bash
git clone <repository-url>
cd gym-management
npm install
```

2. **Set up Supabase** (Follow `scripts/setup-supabase.md`)
   - Create Supabase project
   - Run database schema
   - Configure environment variables

3. **Start Development**
```bash
npm run dev
```

4. **Access the Application**
   - Open http://localhost:3000
   - Use demo credentials to login

## 🔧 Configuration

### Environment Variables

Create `.env.local` with your Supabase credentials:

```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Database Schema

The complete database schema is in `supabase/schema.sql` and includes:

- **Users & Authentication**: Role-based access control
- **Members**: Complete member profiles and status tracking
- **Membership Plans**: Flexible plan management
- **Payments**: Payment tracking with multiple methods
- **Attendance**: Daily check-in/check-out logs
- **Offers**: Discount codes and promotional pricing
- **Settings**: Gym configuration and customization

## 📱 Features Overview

### Dashboard
- Real-time statistics and KPIs
- Quick action buttons
- Alert notifications
- Revenue and member analytics

### Member Management
- Complete member profiles
- Photo and ID proof upload
- Status tracking (active/inactive/suspended)
- Emergency contact information
- Advanced search and filtering

### Payment System
- Multiple payment methods (cash, card, UPI, bank transfer)
- Payment status tracking
- Automatic receipt generation
- Partial payment support
- Overdue payment alerts

### Membership Plans
- Flexible plan creation
- Duration-based pricing
- Feature-based access control
- Category management (Basic, Premium, Family, Student)
- Auto-renewal notifications

### Attendance Tracking
- Daily check-in/check-out
- Visit history and patterns
- Attendance analytics
- Member activity insights

### Reports & Analytics
- Revenue reports (daily, weekly, monthly)
- Member growth trends
- Payment collection analysis
- Attendance patterns
- Offer effectiveness tracking

## 🔒 Security Features

- **Row Level Security (RLS)**: Database-level access control
- **Role-based Authentication**: Admin, Receptionist, Trainer roles
- **Secure API Routes**: Protected endpoints
- **Data Validation**: Input sanitization and validation
- **Audit Logging**: Track user actions and changes

## 🎨 UI/UX Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern Interface**: Clean and intuitive design
- **Dark Mode Ready**: Prepared for dark theme implementation
- **Accessibility**: WCAG compliant components
- **Loading States**: Smooth user experience with loading indicators
- **Toast Notifications**: Real-time feedback for user actions

## 📊 Analytics & Reporting

- **Dashboard Metrics**: Key performance indicators
- **Revenue Tracking**: Income analysis and trends
- **Member Analytics**: Growth and retention metrics
- **Payment Reports**: Collection efficiency tracking
- **Attendance Insights**: Usage patterns and peak hours
- **Export Functionality**: CSV export for external analysis

## 🔧 Customization

### Branding
- Upload gym logo
- Customize color scheme
- Set gym information and contact details
- Configure working hours

### Business Rules
- Set tax rates and currency
- Define membership rules
- Configure payment terms
- Set up notification preferences

### User Roles
- Admin: Full system access
- Receptionist: Member and payment management
- Trainer: Member and attendance access

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
vercel --prod
```

### Docker
```bash
docker build -t gym-management .
docker run -p 3000:3000 gym-management
```

### Manual Deployment
```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- 📖 Documentation: Check the `/scripts` folder for setup guides
- 🐛 Issues: Report bugs via GitHub issues
- 💬 Discussions: Join our community discussions
- 📧 Contact: [Your contact information]

## 🔄 Updates & Roadmap

### Current Version: 1.0.0
- ✅ Core member management
- ✅ Payment tracking
- ✅ Basic reporting
- ✅ Authentication system

### Upcoming Features
- 🔄 SMS/Email notifications
- 🔄 Online payment integration
- 🔄 Mobile app
- 🔄 Advanced analytics
- 🔄 Inventory management
- 🔄 Trainer scheduling
