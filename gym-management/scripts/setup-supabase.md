# Supabase Setup Guide

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create an account
2. Click "New Project"
3. Choose your organization and enter project details:
   - Name: "Gym Management System"
   - Database Password: (choose a strong password)
   - Region: (choose closest to your location)
4. Wait for the project to be created (2-3 minutes)

## 2. Get Your Project Credentials

1. Go to Settings > API in your Supabase dashboard
2. Copy the following values:
   - Project URL
   - Anon (public) key
   - Service role key (keep this secret!)

## 3. Set Up Environment Variables

1. Copy `.env.local.example` to `.env.local`
2. Fill in your Supabase credentials:

```bash
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## 4. Run the Database Schema

1. Go to SQL Editor in your Supabase dashboard
2. Copy the contents of `supabase/schema.sql`
3. <PERSON><PERSON> and run the SQL script
4. This will create all necessary tables, policies, and sample data

## 5. Create Admin Users

Run these SQL commands in the SQL Editor to create demo users:

```sql
-- Insert demo users into auth.users (you'll need to do this via Supabase Auth UI or API)
-- For now, you can create users manually through the Supabase dashboard

-- After creating users in auth, add their profiles:
INSERT INTO profiles (id, email, full_name, role) VALUES
('user-id-1', '<EMAIL>', 'Admin User', 'admin'),
('user-id-2', '<EMAIL>', 'Reception Staff', 'receptionist'),
('user-id-3', '<EMAIL>', 'Gym Trainer', 'trainer');
```

## 6. Enable Row Level Security

The schema already includes RLS policies, but make sure they're enabled:

1. Go to Authentication > Policies in Supabase dashboard
2. Verify that all tables have RLS enabled
3. Check that policies are properly configured

## 7. Configure Storage (Optional)

For member photos and ID proofs:

1. Go to Storage in Supabase dashboard
2. Create a bucket named "member-files"
3. Set up policies for file access

## 8. Test the Setup

1. Run `npm run dev` in your project
2. Go to `http://localhost:3000`
3. Try logging in with demo credentials:
   - <EMAIL> / admin123
   - <EMAIL> / reception123
   - <EMAIL> / trainer123

## Troubleshooting

### Common Issues:

1. **Authentication not working**: Check your environment variables
2. **Database errors**: Verify the schema was applied correctly
3. **Permission errors**: Check RLS policies are properly configured
4. **Missing data**: Run the sample data inserts from schema.sql

### Getting Help:

- Check Supabase documentation: https://supabase.com/docs
- Review the logs in Supabase dashboard
- Check browser console for errors

## Production Deployment

For production:

1. Use environment-specific Supabase projects
2. Set up proper backup strategies
3. Configure custom domains
4. Set up monitoring and alerts
5. Review and tighten security policies
